import { useAppSettings } from "./useAppSettings";

type VideoResolution = {
  width: number;
  height: number;
};

export const useVideoConfig = () => {
  const { settings } = useAppSettings();

  const getVideoResolutionConfig: VideoResolution =
    settings.videoResolution === "720p"
      ? { width: 1280, height: 720 }
      : settings.videoResolution === "4k"
      ? { width: 3840, height: 2160 }
      : { width: 1920, height: 1080 };
  const getFpsConfig = 24;
  const getOptimizedBitRate: "fast" | "normal" | "slow" = "normal";

  // Format timestamp for display
  const getCurrentTimestamp = (): string => {
    return new Date().toLocaleString(settings.dateFormat, {
      hour12: settings.timestampFormat === "12h",
      timeZone: settings.timezone,
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${mins}:${secs}`;
  };

  return {
    getVideoResolutionConfig,
    getFpsConfig,
    getOptimizedBitRate,
    getCurrentTimestamp,
    formatTime,
  };
};
