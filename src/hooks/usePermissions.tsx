// src/hooks/usePermissions.ts

import * as Location from "expo-location";
import * as MediaLibrary from "expo-media-library";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import {
  useCameraPermission,
  useMicrophonePermission,
} from "react-native-vision-camera";
import { LocationData } from "../types/types";

interface PermissionsContextType {
  hasCameraPermission: boolean;
  hasMicrophonePermission: boolean;
  hasLocationPermission: boolean;
  hasMediaLibraryPermission: boolean;
  location: LocationData | null;
  isCheckingPermissions: boolean;
}

const PermissionsContext = createContext<PermissionsContextType | null>(null);

export const PermissionsProvider = ({ children }: { children: ReactNode }) => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(true);

  const {
    hasPermission: hasCameraPermission,
    requestPermission: requestCameraPermission,
  } = useCameraPermission();
  const {
    hasPermission: hasMicrophonePermission,
    requestPermission: requestMicrophonePermission,
  } = useMicrophonePermission();
  const [mediaLibraryPermission, requestMediaLibraryPermission] =
    MediaLibrary.usePermissions();

  async function getAddressFromCoords(
    lat: number,
    lng: number
  ): Promise<string> {
    try {
      const [result] = await Location.reverseGeocodeAsync({
        latitude: lat,
        longitude: lng,
      });
      return result
        ? [result.name, result.street, result.city, result.country]
            .filter(Boolean)
            .join(", ")
        : "Address not found";
    } catch {
      return "Unable to fetch address";
    }
  }

  async function getCurrentLocation(): Promise<void> {
    const currentLocation = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced,
    });
    const { latitude: lat, longitude: lng } = currentLocation.coords;
    const address = await getAddressFromCoords(lat, lng);
    setLocation({ lat, lng, address });
  }

  useEffect(() => {
    (async () => {
      setIsCheckingPermissions(true);
      await requestCameraPermission();
      await requestMicrophonePermission();
      await requestMediaLibraryPermission();
      const locationResult = await Location.requestForegroundPermissionsAsync();

      const locationGranted = locationResult.status === "granted";
      setHasLocationPermission(locationGranted);
      if (locationGranted) {
        await getCurrentLocation();
      }
      setIsCheckingPermissions(false);
    })();
  }, []);

  return (
    <PermissionsContext.Provider
      value={{
        hasCameraPermission,
        hasMicrophonePermission,
        hasLocationPermission,
        hasMediaLibraryPermission: mediaLibraryPermission?.granted ?? false,
        location,
        isCheckingPermissions,
      }}
    >
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = () => {
  const ctx = useContext(PermissionsContext);
  if (!ctx) {
    throw new Error("usePermissions must be used inside a PermissionsProvider");
  }
  return ctx;
};
