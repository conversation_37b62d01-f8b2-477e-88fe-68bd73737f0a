import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import * as MediaLibrary from "expo-media-library";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Alert, Pressable, StyleSheet, Text, View } from "react-native";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
  Camera,
  useCameraDevice,
  useCameraFormat,
  VideoFile,
} from "react-native-vision-camera";
import { useAppSettings } from "../hooks/useAppSettings";
import { usePermissions } from "../hooks/usePermissions";
import { useVideoConfig } from "../hooks/useVideoConfig";

interface CameraViewProps {
  onVideoRecorded?: (video: VideoFile) => void;
}

export default function CameraView({ onVideoRecorded }: CameraViewProps) {
  const device = useCameraDevice("back");
  const cameraRef = useRef<Camera>(null);

  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);

  // Use Reanimated shared value instead of Animated.Value
  const recordingOpacity = useSharedValue(0);

  const insets = useSafeAreaInsets();
  const { location, hasMediaLibraryPermission } = usePermissions();
  const { settings } = useAppSettings();
  const {
    getCurrentTimestamp,
    formatTime,
    getVideoResolutionConfig,
    getFpsConfig,
  } = useVideoConfig();

  const format = useCameraFormat(device, [
    { videoResolution: getVideoResolutionConfig },
    { fps: getFpsConfig },
    { videoStabilizationMode: "auto" },
  ]);

  // Animated style for the recording dot using Reanimated
  const recordingDotStyle = useAnimatedStyle(() => ({
    opacity: recordingOpacity.value,
  }));

  // Function to stop the blinking animation
  const stopBlinkingAnimation = useCallback(() => {
    recordingOpacity.value = withTiming(0, { duration: 200 });
  }, [recordingOpacity]);

  useEffect(() => {
    let intervalId: number;

    if (isRecording) {
      // Start infinite blinking animation using Reanimated
      recordingOpacity.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 500 }),
          withTiming(0, { duration: 500 })
        ),
        -1, // Infinite repeat
        false // Don't reverse
      );

      // Timer for recording duration
      intervalId = setInterval(() => setRecordingTime((t) => t + 1), 1000);
    } else {
      // Stop the blinking animation and reset
      runOnJS(stopBlinkingAnimation)();
      setRecordingTime(0);
    }

    return () => clearInterval(intervalId);
  }, [isRecording, recordingOpacity, stopBlinkingAnimation]);

  const saveVideoToGallery = useCallback(
    async (path: string) => {
      try {
        if (!hasMediaLibraryPermission) {
          Alert.alert(
            "Permission Denied",
            "Cannot save video without Media Library permission."
          );
          return;
        }

        const asset = await MediaLibrary.createAssetAsync(path);
        const albumName = "CameraApp";
        let album = await MediaLibrary.getAlbumAsync(albumName);

        if (album) {
          await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
          console.log("📁 Video saved to existing album");
        } else {
          await MediaLibrary.createAlbumAsync(albumName, asset, false);
          console.log("📁 Album created and video saved");
        }
      } catch (err: any) {
        console.error("❌ Save error:", err);
        Alert.alert("Save Error", err.message || "Failed to save video");
      }
    },
    [hasMediaLibraryPermission]
  );

  const handleRecordingFinished = useCallback(
    async (video: VideoFile) => {
      console.log(video, "dddddddddddddd");
      try {
        setIsRecording(false);
        const compressedPath = video.path;

        await saveVideoToGallery(compressedPath);
        onVideoRecorded?.({ ...video, path: compressedPath });
      } catch (_err) {
        await saveVideoToGallery(video.path);
        onVideoRecorded?.(video);
      }
    },
    [onVideoRecorded, saveVideoToGallery]
  );

  const startRecording = useCallback(async () => {
    if (!cameraRef.current || !device) {
      return Alert.alert("Error", "Camera not available");
    }

    try {
      setIsRecording(true);

      cameraRef.current.startRecording({
        flash: "off",
        fileType: "mp4",
        videoCodec: "h264",
        onRecordingFinished: handleRecordingFinished,
        onRecordingError: (error) => {
          console.error("❌ Recording error:", error);
          setIsRecording(false);
          Alert.alert("Recording Error", error.message || "Failed to record");
        },
      });
    } catch (err: any) {
      setIsRecording(false);
      console.error("❌ Failed to start recording:", err);
      Alert.alert("Error", err.message || "Failed to start recording");
    }
  }, [device, handleRecordingFinished]);

  const stopRecording = useCallback(async () => {
    if (!cameraRef.current || !isRecording) return;
    try {
      console.log("⏹️ Stopping recording...");
      await cameraRef.current.stopRecording();
    } catch (err: any) {
      console.error("❌ Stop error:", err);
      setIsRecording(false);
    }
  }, [isRecording]);

  const handleRecordPress = () =>
    isRecording ? stopRecording() : startRecording();

  if (!device) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="camera-alt" size={64} color="#666" />
        <Text style={styles.errorText}>No camera device available</Text>
      </View>
    );
  }

  console.log(settings, "sssssssssss");

  return (
    <View style={styles.container}>
      <Camera
        ref={cameraRef}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        video={true}
        audio={true}
        format={format}
        fps={getFpsConfig}
        pixelFormat="yuv"
        enableLocation={settings.locationTagging}
        lowLightBoost={true}
        videoBitRate={"extra-low"}
        outputOrientation="device"
      />

      <View style={[styles.overlayContainer, { bottom: insets.bottom + 140 }]}>
        {isRecording && (
          <View style={styles.recordingIndicator}>
            <Animated.View style={[styles.recordingDot, recordingDotStyle]} />
            <Text style={styles.recordingText}>REC</Text>
            <Text style={styles.recordingTime}>
              {formatTime(recordingTime)}
            </Text>
          </View>
        )}
        <Text style={styles.timestampText}>{getCurrentTimestamp()}</Text>
        {settings.locationTagging && location && (
          <>
            <Text style={styles.locationText}>
              {location.address || "Location unavailable"}
            </Text>
            <Text style={styles.locationText}>
              Lat: {location.lat.toFixed(4)}, Lng: {location.lng.toFixed(4)}
            </Text>
          </>
        )}
      </View>

      <View style={[styles.bottomControls, { bottom: insets.bottom + 30 }]}>
        <Pressable
          style={[
            styles.recordButton,
            isRecording && styles.recordButtonRecording,
          ]}
          onPress={handleRecordPress}
        >
          <View
            style={[
              styles.recordButtonInner,
              isRecording && styles.recordButtonInnerRecording,
            ]}
          />
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#000" },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: { color: "white", fontSize: 18, marginTop: 16 },
  overlayContainer: {
    position: "absolute",
    left: 20,
    right: 20,
    alignItems: "center",
  },
  recordingIndicator: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    backgroundColor: "rgba(0,0,0,0.5)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  recordingDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#FF0000",
    marginRight: 8,
  },
  recordingText: {
    color: "#FF0000",
    fontSize: 14,
    fontWeight: "bold",
    marginRight: 8,
  },
  recordingTime: { color: "white", fontSize: 14, fontWeight: "600" },
  timestampText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
    textShadowColor: "rgba(0, 0, 0, 0.75)",
    textShadowOffset: { width: -1, height: 1 },
    textShadowRadius: 10,
  },
  locationText: {
    color: "#4CAF50",
    fontSize: 12,
    marginTop: 6,
    textAlign: "center",
    fontWeight: "bold",
    textShadowColor: "rgba(0, 0, 0, 0.75)",
    textShadowOffset: { width: -1, height: 1 },
    textShadowRadius: 10,
  },
  bottomControls: {
    position: "absolute",
    left: 0,
    right: 0,
    alignItems: "center",
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 4,
    borderColor: "white",
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  },
  recordButtonRecording: { borderColor: "#FF0000" },
  recordButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "white",
  },
  recordButtonInnerRecording: {
    width: 30,
    height: 30,
    borderRadius: 8,
    backgroundColor: "#FF0000",
  },
});
