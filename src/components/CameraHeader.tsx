import Feather from "@expo/vector-icons/Feather";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useRouter } from "expo-router";
import React from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useAppSettings } from "../hooks/useAppSettings";

export default function CameraHeader() {
  const insets = useSafeAreaInsets();
  const { push } = useRouter();

  const { settings, updateSettings } = useAppSettings();

  function navigateToSetting() {
    push("/setting");

    // console.log(settings);
  }

  const cycleResolution = () => {
    const resolutions = ["720p", "1080p", "4k", "auto"];
    const currentIndex = resolutions.indexOf(settings.videoResolution);
    const nextIndex = (currentIndex + 1) % resolutions.length;
    const newResolution = resolutions[nextIndex] as any;
    updateSettings({ videoResolution: newResolution });
  };

  // Toggle location on/off
  async function handleLocationToggle() {
    updateSettings({ locationTagging: !settings.locationTagging });
  }

  return (
    <View
      style={{
        position: "absolute",
        top: insets.top + 4,
        flexDirection: "row",
        justifyContent: "space-between",
        width: "100%",
        paddingHorizontal: 12,
        alignItems: "center",
        zIndex: 10,
      }}
    >
      <Pressable
        onPress={navigateToSetting}
        style={{
          padding: 12,
          backgroundColor: "#00000085",
          borderRadius: 100,
        }}
      >
        <Feather name="settings" size={24} color="white" />
      </Pressable>

      <Pressable
        onPress={cycleResolution}
        style={{
          backgroundColor: "rgba(0,0,0,0.8)",
          paddingHorizontal: 16,
          paddingVertical: 8,
          borderRadius: 20,
          alignItems: "center",
        }}
      >
        <Text style={{ color: "#fff", fontSize: 12 }}>
          [{settings.videoResolution.toUpperCase()}]
        </Text>
      </Pressable>

      <Pressable
        onPress={handleLocationToggle}
        style={[
          styles.locationBadge,
          { backgroundColor: settings.locationTagging ? "#4CAF50" : "#666" },
        ]}
      >
        <MaterialIcons
          name={settings.locationTagging ? "location-on" : "location-off"}
          size={16}
          color="white"
        />
        <Text style={styles.locationBadgeText}>
          {settings.locationTagging ? "ON" : "OFF"}
        </Text>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  locationBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  locationBadgeText: {
    color: "white",
    fontSize: 11,
    marginLeft: 4,
    fontWeight: "600",
  },
});
