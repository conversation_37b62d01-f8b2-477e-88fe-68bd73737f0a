export const getCurrentTimestamp = (
  timestampFormat: string,
  dateFormat?: string
) => {
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: timestampFormat === "12h",
  };
  return new Date().toLocaleString(dateFormat || "en-US", options);
};

export const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
};

export function getVideoResolutionConfig(videoResolution: string) {
  switch (videoResolution) {
    case "720p":
      return { width: 1280, height: 720 };
    case "1080p":
      return { width: 1920, height: 1080 };
    case "4k":
      return { width: 3840, height: 2160 };
    default:
      return { width: 1280, height: 720 };
  }
}

export function getFpsConfig(videoResolution: string) {
  switch (videoResolution) {
    case "720p":
      return 24;
    case "1080p":
      return 30;
    case "4k":
      return 30;
    default:
      return 24;
  }
}

export function getOptimizedBitRate(videoResolution: string): number {
  let baseBitRate: number;

  switch (videoResolution) {
    case "720p":
      baseBitRate = 2;
      break;
    case "1080p":
      baseBitRate = 5;
      break;
    case "4k":
      baseBitRate = 15;
      break;
    default:
      baseBitRate = 2;
  }

  const fps = getFpsConfig(videoResolution);

  let bitRate = baseBitRate;
  bitRate = (bitRate / 30) * fps; // Adjust for FPS

  bitRate *= 0.6;

  return bitRate;
}
