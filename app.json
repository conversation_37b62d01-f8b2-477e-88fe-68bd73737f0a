{"expo": {"name": "cameraApp", "slug": "cameraApp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "cameraapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "$(PRODUCT_NAME) needs access to your Camera.", "NSMicrophoneUsageDescription": "$(PRODUCT_NAME) needs access to your Microphone."}, "bundleIdentifier": "com.anonymous.cameraApp"}, "android": {"adaptiveIcon": {"backgroundColor": "#E6F4FE", "foregroundImage": "./assets/images/android-icon-foreground.png", "backgroundImage": "./assets/images/android-icon-background.png", "monochromeImage": "./assets/images/android-icon-monochrome.png"}, "edgeToEdgeEnabled": true, "predictiveBackGestureEnabled": false, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"], "package": "com.anonymous.cameraApp"}, "web": {"output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff", "dark": {"backgroundColor": "#000000"}}], ["react-native-vision-camera", {"cameraPermissionText": "$(PRODUCT_NAME) needs access to your Camera.", "enableMicrophonePermission": true, "microphonePermissionText": "$(PRODUCT_NAME) needs access to your Microphone."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}], ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos and videos.", "isAccessMediaLocationEnabled": true}], "react-native-compressor"], "experiments": {"typedRoutes": true, "reactCompiler": true}}}