// app/setting.tsx
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useRouter } from "expo-router";
import React from "react";
import {
  Pressable,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useAppSettings } from "../src/hooks/useAppSettings";

export default function SettingsScreen() {
  const { back } = useRouter();
  const insets = useSafeAreaInsets();
  const { settings, updateSettings } = useAppSettings();

  const SettingRow = ({
    title,
    subtitle,
    onPress,
    rightComponent,
  }: {
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightComponent?: React.ReactNode;
  }) => (
    <Pressable style={styles.settingRow} onPress={onPress}>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {rightComponent}
    </Pressable>
  );

  const resolutionOptions = [
    { value: "720p", label: "720p (HD)" },
    { value: "1080p", label: "1080p (Full HD)" },
    { value: "4k", label: "4K (Ultra HD)" },
    { value: "auto", label: "Auto (Device Best)" },
  ];

  const themeOptions = [
    { value: "system", label: "System Default" },
    { value: "light", label: "Light" },
    { value: "dark", label: "Dark" },
  ];

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <View style={styles.header}>
        <Pressable onPress={back} style={styles.backButton}>
          <MaterialIcons name="arrow-back" size={24} color="white" />
        </Pressable>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Theme Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          {themeOptions.map((option) => (
            <SettingRow
              key={option.value}
              title={option.label}
              onPress={() => updateSettings({ theme: option.value as any })}
              rightComponent={
                <MaterialIcons
                  name={
                    settings.theme === option.value
                      ? "radio-button-checked"
                      : "radio-button-unchecked"
                  }
                  size={24}
                  color="#4CAF50"
                />
              }
            />
          ))}
        </View>

        {/* Video Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Video Settings</Text>
          {resolutionOptions.map((option) => (
            <SettingRow
              key={option.value}
              title={option.label}
              onPress={() =>
                updateSettings({ videoResolution: option.value as any })
              }
              rightComponent={
                <MaterialIcons
                  name={
                    settings.videoResolution === option.value
                      ? "radio-button-checked"
                      : "radio-button-unchecked"
                  }
                  size={24}
                  color="#4CAF50"
                />
              }
            />
          ))}
        </View>

        {/* Timestamp Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Timestamp</Text>
          <SettingRow
            title="24-Hour Format"
            subtitle="Use 24-hour time format"
            rightComponent={
              <Switch
                value={settings.timestampFormat === "24h"}
                onValueChange={(value) =>
                  updateSettings({ timestampFormat: value ? "24h" : "12h" })
                }
                trackColor={{ false: "#767577", true: "#4CAF50" }}
              />
            }
          />
        </View>

        {/* Location Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location</Text>
          <SettingRow
            title="Location Tagging"
            subtitle="Add location information to videos"
            rightComponent={
              <Switch
                value={settings.locationTagging}
                onValueChange={(value) =>
                  updateSettings({ locationTagging: value })
                }
                trackColor={{ false: "#767577", true: "#4CAF50" }}
              />
            }
          />

          {settings.locationTagging && (
            <>
              <SettingRow
                title="Show Coordinates"
                onPress={() =>
                  updateSettings({ locationFormat: "coordinates" })
                }
                rightComponent={
                  <MaterialIcons
                    name={
                      settings.locationFormat === "coordinates"
                        ? "radio-button-checked"
                        : "radio-button-unchecked"
                    }
                    size={24}
                    color="#4CAF50"
                  />
                }
              />
              <SettingRow
                title="Show Address"
                onPress={() => updateSettings({ locationFormat: "address" })}
                rightComponent={
                  <MaterialIcons
                    name={
                      settings.locationFormat === "address"
                        ? "radio-button-checked"
                        : "radio-button-unchecked"
                    }
                    size={24}
                    color="#4CAF50"
                  />
                }
              />
            </>
          )}
        </View>

        {/* Storage Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Storage</Text>
          <SettingRow
            title="Auto-delete videos"
            subtitle={
              settings.autoDeleteDays
                ? `After ${settings.autoDeleteDays} days`
                : "Never"
            }
            rightComponent={
              <Switch
                value={!!settings.autoDeleteDays}
                onValueChange={(value) =>
                  updateSettings({ autoDeleteDays: value ? 30 : null })
                }
                trackColor={{ false: "#767577", true: "#4CAF50" }}
              />
            }
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    color: "white",
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    color: "#4CAF50",
    fontSize: 16,
    fontWeight: "600",
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  settingRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: "#333",
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    color: "white",
    fontSize: 16,
  },
  settingSubtitle: {
    color: "#888",
    fontSize: 12,
    marginTop: 2,
  },
});
