// app/index.tsx

import CameraHeader from "@/src/components/CameraHeader";
import CameraView from "@/src/components/CameraView";
import { useAppSettings } from "@/src/hooks/useAppSettings";
import { usePermissions } from "@/src/hooks/usePermissions";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  ActivityIndicator,
  Linking,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";

const PermissionOverlay = () => {
  const {
    hasCameraPermission,
    hasMicrophonePermission,
    isCheckingPermissions,
  } = usePermissions();
  const { isLoading: isLoadingSettings } = useAppSettings();

  if (isCheckingPermissions || isLoadingSettings) {
    return (
      <View style={styles.overlay}>
        <ActivityIndicator size="large" color="#FFFFFF" />
        <Text style={styles.overlayText}>Initializing...</Text>
      </View>
    );
  }

  if (!hasCameraPermission || !hasMicrophonePermission) {
    return (
      <View style={styles.overlay}>
        <MaterialIcons name="camera-alt" size={64} color="#ccc" />
        <Text style={styles.overlayTitle}>Camera Access Required</Text>
        <Text style={styles.overlayText}>
          This app needs camera and microphone access to record videos.
        </Text>
        <Pressable
          onPress={Linking.openSettings}
          style={styles.permissionButton}
        >
          <Text style={styles.buttonText}>Grant Permissions</Text>
        </Pressable>
      </View>
    );
  }

  return null;
};

export default function Index() {
  return (
    <View style={{ flex: 1, backgroundColor: "#000" }}>
      <CameraHeader />
      <CameraView />
      <PermissionOverlay />
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    padding: 20,
    zIndex: 10,
  },
  overlayTitle: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 16,
    textAlign: "center",
  },
  overlayText: {
    color: "#ccc",
    fontSize: 14,
    marginTop: 8,
    textAlign: "center",
  },
  permissionButton: {
    backgroundColor: "#4CAF50",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 20,
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
  },
});
